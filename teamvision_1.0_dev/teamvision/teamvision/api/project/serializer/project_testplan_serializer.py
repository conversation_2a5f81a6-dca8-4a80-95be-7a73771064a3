# coding=utf-8
"""
Created on 2016-10-12

@author: z<PERSON><PERSON><PERSON>
"""
from django.contrib.auth.models import User
from rest_framework import serializers

from business.project.project_task_service import ProjectTaskStatusService
from business.project.tag_service import TagService
from business.project.version_service import VersionService
from teamvision.api.project.serializer.project_requirement_serializer import ProjectRequirementListSerializer
from teamvision.api.project.serializer.project_serializer import ProjectForTestingListSerializer
from teamvision.project import models
from business.auth_user.user_service import UserService
from gatesidelib.datetimehelper import DateTimeHelper
from teamvision.api.project.serializer.project_testcase_serializer import ProjectTestCaseSerializer, ProjectTestCaseDirSerializer
from teamvision.project.models import ProjectTestCaseExecResult
from teamvision.resources.project.resource_string import Fortesting


class ProjectTestPlanSerializer(serializers.ModelSerializer):
    view_data = serializers.SerializerMethodField()
    Fortestings = serializers.SerializerMethodField()
    CreationTime = serializers.DateTimeField(format='%Y-%m-%d %H:%M:%S', read_only=True)
    StartTime = serializers.DateTimeField(format='%Y-%m-%d %H:%M:%S', read_only=True)

    def get_view_data(self, obj):
        result = dict()

        case_count, passed_case, failed_case, blocked_case, retest_case, no_run = self.get_case_result_count(obj)
        result["passed_case"] = passed_case
        result["failed_case"] = failed_case
        result["blocked_case"] = blocked_case
        result["retest_case"] = retest_case
        result["no_run"] = no_run
        result["case_count"] = case_count

        if hasattr(obj, "isplanlist"):
            result["unpass_case"] = []
        else:
            result["unpass_case"] = self.get_uppass_case_result(obj)
        result["creator_name"] = self.creator_name(obj)
        result["project_case_count"] = self.get_case_count(obj)
        if result["case_count"] != 0:
            result["pass_rate"] = round(100 * result["passed_case"] / result["case_count"], 2)
            result["fail_rate"] = round(100 * result["failed_case"] / result["case_count"], 2)
            result["retest_rate"] = round(100 * result["retest_case"] / result["case_count"], 2)
            result["blocked_rate"] = round(100 * result["blocked_case"] / result["case_count"], 2)
            result["norun_rate"] = round(100 * result["no_run"] / result["case_count"], 2)
        else:
            result["pass_rate"] = 0
            result["fail_rate"] = 0
            result["retest_rate"] = 0
            result["blocked_rate"] = 0
            result["norun_rate"] = 0

        if result["project_case_count"] != 0:
            result["case_coverage"] = round(100 * result["case_count"] / result["project_case_count"], 2)
            if result["case_coverage"] > 100:
                result["case_coverage"] = 100
        else:
            result["case_coverage"] = 0

        if obj.Status == 2:
            result["plan_duration"] = self.get_plan_duration(obj)
        else:
            result["plan_duration"] = 0

        fortesting_ids = models.ProjectTestPlanForTesting.objects.filter(TestPlan=obj.id).values_list('Fortesting', flat=True)
        require_ids = models.RequirementTaskMap.objects.filter(TaskID__in=fortesting_ids).values_list('RequirementID', flat=True)
        requirement_ins = models.Requirement.objects.filter(id__in=require_ids)
        requirements = ProjectRequirementListSerializer(instance=requirement_ins, many=True)
        result["requirements"] = requirements.data
        result["Status"] = self.get_status_label(obj)
        result["version_name"] = self.get_version_name(obj)

        fortesting_list = models.TestApplication.objects.filter(id__in=fortesting_ids)
        fortesting_ins = ProjectForTestingListSerializer(instance=fortesting_list, many=True)
        result['fortestings'] = fortesting_ins.data
        return result

    def get_case_result_count(self, test_plan_id):
        plan_case = models.ProjectTestPlanCase.objects.all().filter(TestPlan=test_plan_id).filter(IsGroup=False)
        case_count = plan_case.count()
        passed_case = plan_case.filter(TestResult=1).count()
        failed_case = plan_case.filter(TestResult=4).count()
        blocked_case = plan_case.filter(TestResult=2).count()
        retest_case = plan_case.filter(TestResult=3).count()
        no_run = plan_case.filter(TestResult=0).count()
        return case_count, passed_case, failed_case, blocked_case, retest_case, no_run

    def get_case_result_count_bak(self, obj, case_result):
        plan_case = models.ProjectTestPlanCase.objects.get_plan_cases(obj.id).filter(IsGroup=False)
        if case_result is not None:
            plan_case = plan_case.filter(TestResult=case_result)
        return plan_case.count()

    def get_status_label(self, obj):
        if obj.Status == 1:
            return "新建"
        if obj.Status == 2:
            return "测试中"
        if obj.Status == 3:
            return "已完成"
        if obj.Status == 4:
            return "已归档"
        if obj.Status == 5:
            return "暂停"

    def get_uppass_case_result(self, obj):

        result = list()
        # unpass_plan_case = models.ProjectTestPlanCase.objects.get_plan_cases(obj.id).filter(IsGroup=False).exclude(TestResult__in=[1])
        # test_case_dt,tag_dt,user_dt,test_result_dt,prior_dt = dict(),dict(),{0: '管理员'},dict(),dict()
        #
        # test_case = models.ProjectTestCase.objects.all().filter(IsGroup=False)
        # for _case in test_case:
        #      test_case_dt[_case.__dict__.get('id')] = _case.__dict__.get('Title')
        #      prior_dt[_case.__dict__.get('id')] = str(_case.__dict__.get('Priority')) if _case.__dict__.get('Priority') else '0'
        # tags = models.Tag.objects.filter(TagType=6)
        # for _tag in tags:
        #     tag_dt[str(_tag.__dict__.get('TagValue'))] = _tag.__dict__.get('TagName')
        # autu_user_info = User.objects.filter(is_active=1)
        # for auth_user in autu_user_info:
        #     user_dt[str(auth_user.__dict__.get('id'))] = auth_user.__dict__.get('last_name') + auth_user.__dict__.get('first_name')
        # test_result = models.ProjectTaskStatus.objects.all().filter(Type=7)
        # for _test_result in test_result:
        #     test_result_dt[_test_result.__dict__.get('Status')] = _test_result.__dict__.get('Desc')
        # for case_result in unpass_plan_case:
        #     tp_dt  = dict()
        #     tp_dt['id'] = case_result.__dict__.get('id')
        #     tp_dt['Title'] = test_case_dt.get(case_result.__dict__.get('TestCase'),'测试case被删除')
        #     tp_dt['Priority'] = tag_dt[prior_dt.get(case_result.__dict__.get('TestCase'),'0')]
        #     tp_dt['OwnerName'] = user_dt.get(str(case_result.__dict__.get('Owner')),'管理员')
        #     tp_dt['TestResultName'] =  test_result_dt[case_result.__dict__.get('TestResult')]
        #     tp_dt['CreationTime'] = case_result.__dict__.get('CreationTime')
        #     tp_dt['TestPlan'] = case_result.__dict__.get('TestPlan')
        #     tp_dt['TestCase'] = case_result.__dict__.get('TestCase')
        #     tp_dt['IsGroup'] = case_result.__dict__.get('IsGroup')
        #     tp_dt['IsChecked'] = case_result.__dict__.get('IsChecked')
        #     tp_dt['TestResult'] = case_result.__dict__.get('TestResult')
        #     tp_dt['Creator'] = case_result.__dict__.get('Creator')
        #     tp_dt['Owner'] = case_result.__dict__.get('Owner')
        #
        #     result.append(tp_dt)
        return result

    def creator_name(self, obj):
        return UserService.get_name_by_id(obj.Creator)

    def get_case_count(self, obj):
        if hasattr(obj, 'case_list_count'):
            return obj.case_list_count
        else:
            case_list_count = models.ProjectTestCase.objects.all().filter(Project=obj.Project).filter(IsGroup=0).count()
            return case_list_count

    def get_plan_duration(self, obj):
        diff = DateTimeHelper.get_time_to_now(str(obj.CreationTime)[:19], "%Y-%m-%d %H:%M:%S")
        if diff / 24 / 3600 > 1:
            return int(diff / 24 / 3600)
        else:
            return 1

    def get_Fortestings(self, obj):
        result = models.ProjectTestPlanForTesting.objects.filter(TestPlan=obj.id).values_list('Fortesting', flat=True)
        return result

    def get_version_name(self, obj):
        return VersionService.get_version_name(obj.Version)

    class Meta:
        model = models.ProjectTestPlan
        exclude = ('IsActive',)
        read_only_fields = ('id',)
        extra_kwargs = {'IsActive': {'required': False}}


class ProjectVersionTestPlanSerializer(serializers.ModelSerializer):
    view_data = serializers.SerializerMethodField()
    CreationTime = serializers.DateTimeField(format='%Y-%m-%d %H:%M:%S', read_only=True)
    StartTime = serializers.DateTimeField(format='%Y-%m-%d %H:%M:%S', read_only=True)
    FinishTime = serializers.DateTimeField(format='%Y-%m-%d %H:%M:%S', read_only=True)

    def get_view_data(self, obj):
        result = dict()
        case_count = obj.CaseCount
        if obj.pass_rate is None:
            passed_case = models.ProjectTestPlanCase.objects.filter(TestPlan=obj.id).filter(IsGroup=False).filter(TestResult=1).count()
            if case_count != 0:
                result["pass_rate"] = round(100 * passed_case / case_count, 2)
            else:
                result["pass_rate"] = 0
        else:
            result["pass_rate"] = obj.pass_rate

        result["creator_name"] = self.creator_name(obj)
        if obj.Status == 2:
            result["plan_duration"] = self.get_plan_duration(obj)
        else:
            result["plan_duration"] = 0
        return result

    def creator_name(self, obj):
        return UserService.get_name_by_id(obj.Creator)

    def get_plan_duration(self, obj):
        diff = DateTimeHelper.get_time_to_now(str(obj.CreationTime)[:19], "%Y-%m-%d %H:%M:%S")
        if diff / 24 / 3600 > 1:
            return int(diff / 24 / 3600)
        else:
            return 1

    class Meta:
        model = models.ProjectTestPlan
        exclude = ('IsActive', 'Desc', 'IncludeAllCase', 'history')
        read_only_fields = ('id',)
        extra_kwargs = {'IsActive': {'required': False}}


class ProjectTestPlanSimpleSerializer(serializers.ModelSerializer):
    view_data = serializers.SerializerMethodField()
    CreationTime = serializers.DateTimeField(format='%Y-%m-%d %H:%M:%S', read_only=True)
    StartTime = serializers.DateTimeField(format='%Y-%m-%d %H:%M:%S', read_only=True)
    FinishTime = serializers.DateTimeField(format='%Y-%m-%d %H:%M:%S', read_only=True)

    def get_view_data(self, obj):
        result = dict()
        result["fortestings"] = self.get_fortestings(obj)
        result["status_name"] = self.get_status_name(obj)
        result["creator_name"] = self.get_creator_name(obj)
        return result

    def get_fortestings(self, obj):
        result = list()
        plan_fortestings = models.ProjectTestPlanForTesting.objects.get_plan_fortesting(obj.id)
        for fortesting in plan_fortestings:
            result.append(fortesting.Fortesting)
        return result

    def get_status_name(self, obj):
        if obj.Status == 1:
            return "新建"
        if obj.Status == 2:
            return "测试中"
        if obj.Status == 3:
            return "已完成"
        if obj.Status == 4:
            return "已归档"
        if obj.Status == 5:
            return "暂停"

    def get_creator_name(self, obj):
        return UserService.get_name_by_id(obj.Creator)

    class Meta:
        model = models.ProjectTestPlan
        exclude = ('IsActive',)
        read_only_fields = ('id',)
        extra_kwargs = {'IsActive': {'required': False}}


class ProjectTestPlanCaseSerializer(serializers.ModelSerializer):
    CreationTime = serializers.DateTimeField(format='%Y-%m-%d %H:%M:%S', read_only=True)
    result = serializers.SerializerMethodField()
    owner_name = serializers.SerializerMethodField()
    test_case = serializers.SerializerMethodField()
    exec_result = serializers.SerializerMethodField()

    def get_test_case(self, obj):
        test_case = ProjectTestCaseSerializer(obj.test_case)
        return test_case.data

    def get_exec_result(self, obj):
        case_exec_result_list = models.ProjectTestCaseExecResult.objects.select_related('case_id').filter(case_id=obj.id)
        case_exec_result = ProjectTestCaseExecResultSerializer(case_exec_result_list, many=True)
        return case_exec_result.data

    def get_result(self, obj):
        """
        0 未开始 1通过 2 受阻碍 3 重测 4 失败
        """
        if obj.TestResult == 0:
            return '未开始'
        if obj.TestResult == 1:
            return '通过'
        if obj.TestResult == 2:
            return '受阻碍'
        if obj.TestResult == 3:
            return '重测'
        if obj.TestResult == 4:
            return '失败'
        return '--'

    def get_owner_name(self, obj):
        return UserService.get_name_by_id(obj.Owner)

    class Meta:
        model = models.ProjectTestPlanCase
        exclude = ('IsActive',)
        read_only_fields = ('id',)
        extra_kwargs = {'IsActive': {'required': False}}


class ProjectTestPlanCaseListSerializer(serializers.ModelSerializer):
    CreationTime = serializers.DateTimeField(format='%Y-%m-%d %H:%M:%S', read_only=True)
    title = serializers.SerializerMethodField()
    result = serializers.SerializerMethodField()
    owner_name = serializers.SerializerMethodField()

    def get_title(self, obj):
        test_case = obj.test_case.Title
        if test_case:
            return test_case
        else:
            return '--'

    def get_result(self, obj):
        """
        '0 未开始 1通过 2 受阻碍 3 重测 4 失败'
        """""
        if obj.TestResult == 0:
            return '未开始'
        if obj.TestResult == 1:
            return '通过'
        if obj.TestResult == 2:
            return '受阻碍'
        if obj.TestResult == 3:
            return '重测'
        if obj.TestResult == 4:
            return '失败'
        return '--'

    def get_owner_name(self, obj):
        return UserService.get_name_by_id(obj.Owner)

    class Meta:
        model = models.ProjectTestPlanCase
        exclude = ('IsActive',)
        read_only_fields = ('id',)
        extra_kwargs = {'IsActive': {'required': False}}


class ProjectTestPlanVersionSerializer(serializers.ModelSerializer):
    view_data = serializers.SerializerMethodField()

    def get_view_data(self, obj):
        result = dict()
        version_testplans = models.ProjectTestPlan.objects.get_version_plans(obj.id)

        test_plan_ids = [plan.id for plan in version_testplans]
        result["version_status"] = self.version_status(obj)
        result["date_range"] = self.date_range(obj)
        result["test_plans"] = self.version_test_plans(obj, version_testplans)

        case_count, passed_case, failed_case, blocked_case, retest_case, no_run = self.get_case_result_count(obj, version_testplans)
        result["case_count"] = case_count
        result["passed_case"] = passed_case
        result["failed_case"] = failed_case
        result["blocked_case"] = blocked_case
        result["retest_case"] = retest_case
        result["no_run"] = no_run

        result["project_case_count"] = self.get_case_count(obj)
        if result["case_count"] != 0:
            result["pass_rate"] = round(100 * result["passed_case"] / result["case_count"], 2)
            result["fail_rate"] = round(100 * result["failed_case"] / result["case_count"], 2)
            result["retest_rate"] = round(100 * result["retest_case"] / result["case_count"], 2)
            result["blocked_rate"] = round(100 * result["blocked_case"] / result["case_count"], 2)
            result["norun_rate"] = round(100 * result["no_run"] / result["case_count"], 2)
        else:
            result["pass_rate"] = 0
            result["fail_rate"] = 0
            result["retest_rate"] = 0
            result["blocked_rate"] = 0
            result["norun_rate"] = 0

        if result["project_case_count"] != 0:
            result["case_coverage"] = round(100 * result["case_count"] / result["project_case_count"], 2)
            if result["case_coverage"] > 100:
                result["case_coverage"] = 100
        else:
            result["case_coverage"] = 0

        return result

    def get_case_result_count(self, obj, version_testplans):
        plan_case = models.ProjectTestPlanCase.objects.all().filter(TestPlan__in=version_testplans).filter(IsGroup=False)

        case_count = plan_case.count()
        passed_case = plan_case.filter(TestResult=1).count()
        failed_case = plan_case.filter(TestResult=4).count()
        blocked_case = plan_case.filter(TestResult=2).count()
        retest_case = plan_case.filter(TestResult=3).count()
        no_run = plan_case.filter(TestResult=0).count()
        return (case_count, passed_case, failed_case, blocked_case, retest_case, no_run)

    def get_case_count(self, obj):
        if hasattr(obj, 'case_list_count'):
            return obj.case_list_count
        else:
            obj.case_list_count = models.ProjectTestCase.objects.all().filter(Project=obj.VProjectID).filter(
                IsGroup=0).count()
            return obj.case_list_count

    def version_test_plans(self, obj, test_plans):
        result = list()
        # test_plans = models.ProjectTestPlan.objects.get_version_plans(obj.id)
        for test_plan in test_plans:
            test_plan.isplanlist = True
            test_plan.project_case_count = self.get_case_count(obj)
            serializer = ProjectTestPlanSerializer(instance=test_plan)
            result.append(serializer.data)
        return result

    def date_range(self, obj):
        result = "未设置时间范围"
        if obj.VStartDate and obj.VReleaseDate:
            result = str(obj.VStartDate) + '-' + str(obj.VReleaseDate)
        return result

    def version_status(self, obj):
        result = "进行中"
        if obj.VReleaseDate:
            diff = DateTimeHelper.get_time_to_now(str(obj.VReleaseDate), "%Y-%m-%d")
            if diff > 0:
                result = "已过期"
        return result

    class Meta:
        model = models.Version
        exclude = ('CreationTime', 'IsActive')
        extra_kwargs = {'VDescription': {'required': False}}
        read_only_fields = ('id',)


class ProjectVersionTestPlanListSerializer(serializers.ModelSerializer):
    view_data = serializers.SerializerMethodField()

    def get_view_data(self, obj):
        result = dict()
        result["version_status"] = self.version_status(obj)
        if result["version_status"] == '进行中':
            result["test_plans"] = self.version_test_plans(obj)
        elif obj.show_test_plans == True:
            result["test_plans"] = self.version_test_plans(obj)
        else:
            result["test_plans"] = []
        return result

    def version_test_plans(self, obj):
        result = list()
        test_plans = models.ProjectTestPlan.objects.get_version_plans(obj.id)
        test_plan_ser = ProjectVersionTestPlanSerializer(instance=test_plans, many=True)
        result = test_plan_ser.data
        return result

    def version_status(self, obj):
        result = "进行中"
        if obj.VReleaseDate:
            diff = DateTimeHelper.get_time_to_now(str(obj.VReleaseDate), "%Y-%m-%d")
            if diff > 86400:
                result = "已过期"
        return result

    class Meta:
        model = models.Version
        exclude = ('CreationTime', 'IsActive')
        extra_kwargs = {'VDescription': {'required': False}}
        read_only_fields = ('id',)


class ProjectTestPlanVersionsSerializer(serializers.ModelSerializer):
    view_data = serializers.SerializerMethodField()

    def get_view_data(self, obj):
        result = dict()
        result["version_status"] = self.version_status(obj)
        result["test_plans"] = []
        return result

    def version_status(self, obj):
        result = "进行中"
        if obj.VReleaseDate:
            diff = DateTimeHelper.get_time_to_now(str(obj.VReleaseDate), "%Y-%m-%d")
            if diff > 0:
                result = "已过期"
        return result

    class Meta:
        model = models.Version
        exclude = ('CreationTime', 'IsActive')
        extra_kwargs = {'VDescription': {'required': False}}
        read_only_fields = ('id',)


class ProjectTestCaseExecResultSerializer(serializers.ModelSerializer):
    CreationTime = serializers.DateTimeField(format='%Y-%m-%d %H:%M:%S', read_only=True)
    Result = serializers.SerializerMethodField(method_name="get_result")

    def get_result(self, obj):
        """
        '0 未开始 1通过 2 受阻碍 3 重测 4 失败'
        """
        if obj.TestResult == 0:
            return '未开始'
        if obj.TestResult == 1:
            return '通过'
        if obj.TestResult == 2:
            return '受阻碍'
        if obj.TestResult == 3:
            return '重测'
        if obj.TestResult == 4:
            return '失败'
        return '--'

    class Meta:
        model = models.ProjectTestCaseExecResult
        exclude = ('IsActive',)
        read_only_fields = ('id', 'CreationTime')
        extra_kwargs = {'IsActive': {'required': False}}


class ProjectTestPlanCaseResultSerializer(serializers.ModelSerializer):
    view_data = serializers.SerializerMethodField()
    CreationTime = serializers.DateTimeField(format='%Y-%m-%d %H:%M:%S', read_only=True)

    def get_view_data(self, obj):
        # case_exec_result_list = models.ProjectTestCaseExecResult.objects.select_related('case_id').filter(case_id=obj.id)
        # case_exec_result = ProjectTestCaseExecResultSerializer(case_exec_result_list, many=True)
        # return case_exec_result.data

        result = dict()
        result['Priority'] = self.get_priority(obj)
        result['Title'] = obj.test_case.Title
        result['Owner'] = self.get_owner_name(obj)
        result['TestResultName'] = self.test_result(obj)
        return result

    def get_priority(self, obj):
        name, color = TagService.get_tag_name(6, obj.test_case.Priority)
        return name

    def get_owner_name(self, obj):
        user = UserService.get_name_by_id(obj.Owner)
        return user

    def test_result(self, obj):
        if obj.TestResult == 0:
            return '未开始'
        if obj.TestResult == 1:
            return '通过'
        if obj.TestResult == 2:
            return '受阻碍'
        if obj.TestResult == 3:
            return '重测'
        if obj.TestResult == 4:
            return '失败'
        return '--'

    class Meta:
        model = models.ProjectTestPlanCase
        exclude = ('IsActive',)
        read_only_fields = ('id',)
        extra_kwargs = {'IsActive': {'required': False}}


class ProjectTestPlanCaseTreeCaseSerializer(serializers.ModelSerializer):
    view_data = serializers.SerializerMethodField()
    # CreationTime = serializers.DateTimeField(format='%Y-%m-%d %H:%M:%S', read_only=True)
    Title = serializers.SerializerMethodField()

    def get_view_data(self, obj):
        result = dict()
        result['Priority'] = self.get_priority(obj)
        result['Title'] = obj.test_case.Title
        result['Owner'] = self.get_owner_name(obj)
        return result

    def get_Title(self, obj):
        return obj.test_case.Title

    def get_priority(self, obj):
        name = "P2"
        name, color = TagService.get_tag_name(6, obj.test_case.Priority)
        return name

    def get_owner_name(self, obj):
        user = UserService.get_name_by_id(obj.Owner)
        return user

    class Meta:
        model = models.ProjectTestPlanCase
        exclude = ('IsActive', 'CreationTime')
        read_only_fields = ('id',)
        extra_kwargs = {'IsActive': {'required': False}}


class ProjectTestPlanCaseTreeDirSerializer(serializers.ModelSerializer):
    view_data = serializers.SerializerMethodField()
    Title = serializers.SerializerMethodField()
    CreationTime = serializers.DateTimeField(format='%Y-%m-%d %H:%M:%S', read_only=True)

    def get_view_data(self, obj):
        result = dict()
        result['Title'] = obj.test_case.Title
        return result

    def get_Title(self, obj):
        return obj.test_case.Title

    class Meta:
        model = models.ProjectTestPlanCase
        exclude = ('IsActive', 'TestResult')
        read_only_fields = ('id',)
        extra_kwargs = {'IsActive': {'required': False}}


class ProjectTestPlanCaseTreeSerializer(serializers.ModelSerializer):
    CreationTime = serializers.DateTimeField(format='%Y-%m-%d %H:%M:%S', read_only=True)
    UpdateTime = serializers.DateTimeField(format='%Y-%m-%d %H:%M:%S', read_only=True)
    children = serializers.SerializerMethodField()
    plancase_list = serializers.SerializerMethodField()
    TestResult = serializers.SerializerMethodField()

    def get_TestResult(self, obj):
        return 0

    def get_children(self, obj):
        result = list()
        self.plancaseidlist = []
        test_case_list = models.ProjectTestCase.objects.filter(IsActive=1).filter(id__in=obj.case_ids)
        child_nodes = test_case_list.filter(Parent=obj.id).order_by("IsGroup")
        if len(obj.case_ids) > 0:
            child_nodes = child_nodes.filter(id__in=obj.case_ids)

        test_plan_case_list = models.ProjectTestPlanCase.objects.all().filter(TestPlan=obj.plan_id)
        if child_nodes.count() > 0:
            for child_node in child_nodes:
                self.get_child_data2(child_node, result, test_plan_case_list)
                # if test_plan_case_list[0].Parent:
                #     self.get_child_data2(child_node, result, test_plan_case_list)
                # else:
                #     self.get_child_data(child_node, result, obj.case_ids, test_plan_case_list, test_case_list)
        return result

    def get_child_data(self, parent_node, child_data, case_ids, test_plan_case_list, test_case_list):
        if parent_node:
            temp_result_list = test_plan_case_list.filter(TestCase=parent_node.id)
            if temp_result_list.count() > 0:
                temp_result = temp_result_list[0]
                temp_serializer = ProjectTestPlanCaseTreeCaseSerializer(instance=temp_result)
            else:
                if parent_node.IsGroup:
                    temp_serializer = ProjectTestPlanCaseTreeDirSerializer(instance=parent_node)
                else:
                    return
            temp_data = temp_serializer.data

            if parent_node.IsGroup == False:
                child_data.append(temp_data)
                self.plancaseidlist.append(temp_data["id"])
                return
            temp_data["children"] = list()
            child_nodes = test_case_list.filter(Parent=parent_node.id).order_by("IsGroup")
            if len(case_ids) > 0:
                child_nodes = child_nodes.filter(id__in=case_ids)
            if child_nodes.count() > 0:
                for child_node in child_nodes:
                    self.get_child_data(child_node, temp_data["children"], case_ids, test_plan_case_list, test_case_list)
                child_data.append(temp_data)
            else:
                child_data.append(temp_data)
                return
        else:
            return

    def get_child_data2(self, parent_node, child_data, test_plan_case_list):
        if parent_node:
            temp_result_list = test_plan_case_list.filter(TestCase=parent_node.id)
            if temp_result_list.count() > 0:
                temp_result = temp_result_list[0]
                temp_serializer = ProjectTestPlanCaseTreeCaseSerializer(instance=temp_result)
            else:
                if parent_node.IsGroup:
                    temp_serializer = ProjectTestPlanCaseTreeDirSerializer(instance=parent_node)
                else:
                    return
            temp_data = temp_serializer.data
            if parent_node.IsGroup == False:
                child_data.append(temp_data)
                self.plancaseidlist.append(temp_data["id"])
                return

            temp_data["children"] = list()
            child_nodes = test_plan_case_list.filter(Parent=parent_node.id).order_by("IsGroup")
            if child_nodes.count() > 0:
                for child_node in child_nodes:
                    self.get_child_data2(child_node.test_case, temp_data["children"], test_plan_case_list)
                child_data.append(temp_data)
            else:
                child_data.append(temp_data)
                return
        else:
            return

    def get_plancase_list(self, obj):
        return self.plancaseidlist

    class Meta:
        model = models.ProjectTestCase
        exclude = ('IsActive', 'Module', 'Scenes', 'automate', 'automatedCompletion', 'RunTimes', 'Precondition', 'ExpectResult',
                   'Priority', 'Status')
        read_only_fields = ('id', 'CreationTime', 'UpdateTime')
