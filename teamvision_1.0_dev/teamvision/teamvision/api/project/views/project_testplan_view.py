# coding=utf-8

"""
Created on 2014-1-5

@author: z<PERSON><PERSON><PERSON>
"""
from django.contrib.auth.models import User
from rest_framework import generics, status, response
from datetime import date, timedelta
from business.auth_user.user_service import UserService
from business.common.datetime_service import DateTimeService
from business.project.version_service import VersionService
from gatesidelib.common.simplelogger import SimpleLogger
from gatesidelib.datetimehelper import DateTimeHelper
from teamvision.api.project.serializer import project_testplan_serializer
from teamvision.api.project.serializer.project_testplan_serializer import ProjectTestPlanSerializer, ProjectTestPlanSimpleSerializer
from teamvision.api.project.views.CsrfExemptSessionAuthentication import CsrfExemptSessionAuthentication
from rest_framework.authentication import SessionAuthentication, BasicAuthentication
from rest_framework.permissions import AllowAny, IsAuthenticated
from business.project.project_testplan_service import TestPlanService
from teamvision.api.project.filters import project_testplan_filter
from teamvision.project import models
from teamvision.project.models import Project, Version, ProjectTestPlanForTesting, ProjectTestPlan, ProjectTestPlanCase, ProjectTestCase
from rest_framework.views import APIView
from business.project.project_service import ProjectService


class ProjectTestPlanCreateView(generics.CreateAPIView):
    """
    /api/project/testplan/create
    """
    serializer_class = project_testplan_serializer.ProjectTestPlanSerializer
    authentication_classes = (CsrfExemptSessionAuthentication, BasicAuthentication)
    permission_classes = [AllowAny]

    def create(self, request, *args, **kwargs):
        test_plan = TestPlanService.create_testplan(request.data, request.user)
        serializer = project_testplan_serializer.ProjectTestPlanSerializer(instance=test_plan)
        TestPlanService.send_test_plan_email(serializer.data)
        headers = self.get_success_headers(serializer.data)
        return response.Response(serializer.data, status=status.HTTP_201_CREATED, headers=headers)


class ProjectTestPlanCaseView(generics.RetrieveAPIView):
    """
    /api/project/testplan/case/<case_id>
    """
    serializer_class = project_testplan_serializer.ProjectTestPlanCaseSerializer
    authentication_classes = (CsrfExemptSessionAuthentication, BasicAuthentication)
    permission_classes = [AllowAny]

    def get_object(self):
        case_id = self.kwargs.get("case_id")
        qs = ProjectTestPlanCase.objects.get(int(case_id))
        return qs


class ProjectTestPlanUpdateTestCaseView(APIView):
    """
        /api/project/testplan/testplancases/<plan_id>
    """
    authentication_classes = (CsrfExemptSessionAuthentication, BasicAuthentication)
    permission_classes = [AllowAny]

    def patch(self, request, *args, **kwargs):
        plan_id = self.kwargs.get('plan_id')
        user_id = request.user.id
        owner = request.data.get("Owner", 0)
        test_plan = models.ProjectTestPlan.objects.get(id=plan_id)
        checked_group = request.data.get("SelectCaseGroup", [])
        for group in checked_group:
            testcase = models.ProjectTestCase.objects.get(id=group)
            if testcase.IsGroup == True:
                self.update_testplan_case(test_plan, testcase, True, user_id, owner)
            else:
                self.update_testplan_case(test_plan, testcase, False, user_id, owner)

        half_check_group = request.data.get("halfCheckCaseGroup", [])
        for group in half_check_group:
            testcase = models.ProjectTestCase.objects.get(id=group)
            self.update_testplan_case(test_plan, testcase, False, user_id, owner)

        TestPlanService.update_testplan_casecount(test_plan)

        return response.Response(status=status.HTTP_201_CREATED)

    def update_testplan_case(self, test_plan, case, checked, user_id, owner):
        result = ProjectTestPlanCase.objects.filter(TestPlan=test_plan).filter(TestCase=case.id).exists()
        if result == False:
            plan_case = ProjectTestPlanCase()
            plan_case.IsGroup = case.IsGroup
            plan_case.IsChecked = checked
            plan_case.Creator = user_id
            plan_case.Owner = owner
            plan_case.TestCase = case
            plan_case.TestPlan = test_plan
            plan_case.TestResult = 0
            plan_case.Parent = case.Parent
            plan_case.save()


class ProjectTestPlanView(generics.RetrieveUpdateDestroyAPIView):
    """
    /api/project/testplan/<plan_id>
    """
    serializer_class = project_testplan_serializer.ProjectTestPlanSerializer
    authentication_classes = (CsrfExemptSessionAuthentication, BasicAuthentication)
    permission_classes = [AllowAny]

    def get_object(self):
        plan_id = self.kwargs.get('plan_id')
        return ProjectTestPlan.objects.get(plan_id)

    def delete(self, request, *args, **kwargs):
        plan_id = self.kwargs.get('plan_id')
        ProjectTestPlan.objects.filter(id=plan_id).delete()
        ProjectTestPlanCase.objects.filter(TestPlan=plan_id).delete()
        return response.Response(status=status.HTTP_204_NO_CONTENT)

    def patch(self, request, *args, **kwargs):
        partial = kwargs.pop('partial', True)
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data, partial=partial)
        TestPlanService.update_fortestings(instance, request.data.get("fortestings"))
        serializer.is_valid(raise_exception=True)
        serializer.save()

        if getattr(instance, '_prefetched_objects_cache', None):
            # If 'prefetch_related' has been applied to a queryset, we need to
            # forcibly invalidate the prefetch cache on the instance.
            instance._prefetched_objects_cache = {}

        return response.Response(serializer.data)


class ProjectTestPlanListView(generics.ListAPIView):
    """
    /api/project/<project_id>/testplan/list
    """
    serializer_class = project_testplan_serializer.ProjectTestPlanSimpleSerializer
    authentication_classes = (CsrfExemptSessionAuthentication, BasicAuthentication)
    permission_classes = [AllowAny]

    def get_queryset(self):
        project_id = self.kwargs.get("project_id")
        qs = ProjectTestPlan.objects.get_project_plans(project_id)
        return project_testplan_filter.TestPlanFilterSet(data=self.request.GET, queryset=qs).filter()


class ProjectTestPlanSimpleListView(generics.ListAPIView):
    """
    /api/project/<project_id>/testplans
    """
    serializer_class = project_testplan_serializer.ProjectTestPlanSimpleSerializer
    authentication_classes = (CsrfExemptSessionAuthentication, BasicAuthentication)
    permission_classes = [AllowAny]

    def get_queryset(self):
        project_id = self.kwargs.get("project_id")
        qs = ProjectTestPlan.objects.get_project_plans(project_id)
        return project_testplan_filter.TestPlanFilterSet(data=self.request.GET, queryset=qs).filter()


class ProjectVersionTestPlanListView(generics.ListAPIView):
    """
    /api/project/<project_id>/version/testplans?v=0
    """

    serializer_class = project_testplan_serializer.ProjectVersionTestPlanListSerializer
    authentication_classes = (CsrfExemptSessionAuthentication, BasicAuthentication)
    permission_classes = [AllowAny]

    def get_queryset(self):
        project_id = self.kwargs.get("project_id")
        version_id = self.request.GET.get('v', 0)
        if version_id == 0:
            qs = models.Version.objects.get_versions(project_id)
            for obj in qs:
                obj.show_test_plans = False
            return qs
        else:
            qs = models.Version.objects.filter(id=version_id)
            for obj in qs:
                obj.show_test_plans = True
            return qs


class ProjectTestPlanVersionsView(generics.ListAPIView):
    """
    /api/project/<project_id>/testplan/versions
    """
    serializer_class = project_testplan_serializer.ProjectTestPlanVersionsSerializer
    authentication_classes = (CsrfExemptSessionAuthentication, BasicAuthentication)
    permission_classes = [AllowAny]

    def get_queryset(self):
        project_id = self.kwargs.get("project_id")
        qs = models.Version.objects.get_versions(project_id)
        return qs


class ProjectVersionTestPlanView(generics.RetrieveAPIView):
    """
    /api/project/testplan/version/<version_id>
    """
    serializer_class = project_testplan_serializer.ProjectTestPlanVersionSerializer
    authentication_classes = (CsrfExemptSessionAuthentication, BasicAuthentication)
    permission_classes = [AllowAny]

    def get_object(self):
        version_id = self.kwargs.get("version_id")
        qs = models.Version.objects.get(version_id)
        return qs


class ProjectTestPlanCaseResultListView(generics.ListAPIView):
    """
    /api/project/testplan/case_results
    FilterSet: ['id','TestPlan','TestResult','Owner','Version']
    FilterOperation:=,__in,__gt,__contains,__icontains,Range__in,__lt,!=,__isnull
    """
    serializer_class = project_testplan_serializer.ProjectTestPlanCaseResultSerializer
    authentication_classes = (CsrfExemptSessionAuthentication, BasicAuthentication)
    permission_classes = [AllowAny]

    def get_queryset(self):
        version_id = self.request.GET.get("Version", 0)
        if version_id != 0:
            test_plans = ProjectTestPlan.objects.get_version_plans(version_id)
            test_plan_ids = [plan.id for plan in test_plans]
            qs = ProjectTestPlanCase.objects.all().filter(TestPlan__in=test_plan_ids)
        else:
            qs = ProjectTestPlanCase.objects.all()
        return project_testplan_filter.TestPlanCaseFilterSet(data=self.request.GET, queryset=qs).filter()


class ProjectTestPlanCaseResultView(generics.RetrieveUpdateDestroyAPIView):
    """
    /api/project/testplan/case_result/<plan_case_id>
    """
    serializer_class = project_testplan_serializer.ProjectTestPlanCaseResultSerializer
    authentication_classes = (CsrfExemptSessionAuthentication, BasicAuthentication)
    permission_classes = [AllowAny]

    def get_object(self):
        result_id = self.kwargs.get("plan_case_id")
        qs = ProjectTestPlanCase.objects.get(int(result_id))
        return qs

    def patch(self, request, *args, **kwargs):
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data, partial=True)
        serializer.is_valid(raise_exception=True)
        self.perform_update(serializer)

        if getattr(instance, '_prefetched_objects_cache', None):
            instance._prefetched_objects_cache = {}

        test_case_exec_result = models.ProjectTestCaseExecResult()
        test_case_exec_result.case_id = instance
        test_case_exec_result.Owner = request.user.id
        try:
            test_case_exec_result.ActualResult = request.data['ActualResult']
        except Exception as e:
            pass
        test_case_exec_result.TestResult = request.data['TestResult']
        test_case_exec_result.save()

        return response.Response(serializer.data)


class ProjectTestPlanCaseTreeView(generics.ListAPIView):
    """
    /api/project/testplan/<plan_id>/case_tree
    """
    serializer_class = project_testplan_serializer.ProjectTestPlanCaseTreeSerializer
    authentication_classes = (CsrfExemptSessionAuthentication, BasicAuthentication)
    permission_classes = [AllowAny]

    def get_queryset(self):
        tree_list = list()
        plan_id = self.kwargs.get("plan_id")
        test_plan = ProjectTestPlan.objects.get(plan_id)
        case_ids = ProjectTestPlanCase.objects.filter(TestPlan=plan_id).values_list('TestCase', flat=True)
        if test_plan is not None:
            tree_list = models.ProjectTestCase.objects.filter(Project=test_plan.Project).filter(Parent=0).filter(id__in=case_ids)
            for tree in tree_list:
                tree.case_ids = case_ids
                tree.plan_id = plan_id
        return tree_list


class ProjectTestPlanCaseTreeLazyLoadView(generics.ListAPIView):
    """
    /api/project/testplan/<plan_id>/case_tree/lazyload?case_id=1
    """
    serializer_class = project_testplan_serializer.ProjectTestPlanCaseTreeCaseSerializer
    authentication_classes = (CsrfExemptSessionAuthentication, BasicAuthentication)
    permission_classes = [AllowAny]

    def get_queryset(self):
        plan_id = self.kwargs.get("plan_id")
        parent_id = self.request.GET.get('case_id')
        test_case_list = ProjectTestPlanCase.objects.filter(TestPlan=plan_id).filter(Parent=parent_id)
        return test_case_list


class ProjectTestPlanCaseTreeTypeView(generics.ListAPIView):
    """
    /api/project/testplan/<plan_id>/case_tree/type
    """
    serializer_class = project_testplan_serializer.ProjectTestPlanCaseTreeSerializer
    authentication_classes = (CsrfExemptSessionAuthentication, BasicAuthentication)
    permission_classes = [AllowAny]

    def get_queryset(self):
        tree_list, result, case_ids, flag, all_falg = list(), list(), list(), False, False
        plan_id = self.kwargs.get("plan_id")
        type = self.kwargs.get("type")
        if str(type).startswith("_"):
            type = str(type)[1:]
            flag = True
        test_plan = ProjectTestPlan.objects.get(plan_id)
        plan_case_group = ProjectTestPlanCase.objects.get_plan_cases(plan_id)
        if int(type) >= 0:
            for temp_group in plan_case_group:
                if flag:
                    if temp_group.__dict__.get('IsGroup'):
                        case_ids.append(temp_group.__dict__.get('TestCase'))
                    elif str(type) == str(temp_group.__dict__.get('TestResult')):
                        case_ids.append(temp_group.__dict__.get('TestCase'))
                        all_falg = True
                else:
                    if int(type) > 0:
                        if temp_group.__dict__.get('IsGroup'):
                            case_ids.append(temp_group.__dict__.get('TestCase'))
                        elif str(type) == str(temp_group.__dict__.get('Owner')):
                            case_ids.append(temp_group.__dict__.get('TestCase'))
                            all_falg = True
                    else:
                        case_ids = [case.TestCase for case in plan_case_group]
                        all_falg = True
        else:
            case_ids = [case.TestCase for case in plan_case_group]
            all_falg = True
        plan_case_ids = case_ids
        if test_plan is not None:
            tree_list = models.ProjectTestCase.objects.filter(Project=test_plan.Project).filter(Parent=0).filter(
                id__in=plan_case_ids)
        for tree in tree_list:
            if test_plan.IncludeAllCase == 1:
                tree.plan_case_ids = list()
            else:
                tree.plan_case_ids = plan_case_ids
            tree.plan_id = plan_id
        if all_falg:
            return tree_list
        else:
            return list()


class ProjectTestPlanUpdateView(generics.UpdateAPIView):
    """
    /api/project/testplan/planID/update_owners
    """
    serializer_class = project_testplan_serializer.ProjectTestPlanSerializer
    permission_classes = [AllowAny]
    authentication_classes = (CsrfExemptSessionAuthentication, BasicAuthentication)

    def patch(self, request, *args, **kwargs):
        owner = request.data.get("owner", 0)
        ids = request.data.get("ids", 0)
        result = [True, "分配成功"]
        try:
            for id in ids:
                planCaseBean = ProjectTestPlanCase.objects.get(id)
                if planCaseBean:
                    planCaseBean.Owner = owner
                    planCaseBean.save()
        except Exception as ex:
            SimpleLogger.exception(ex)
            result = [False, '分配失败']
        return response.Response({'result': result[0], 'message': result[1]})


class ProjectTestPlanUpdateStatusView(APIView):
    """
    /api/project/testplan/<plan_id>/update_status
    """
    permission_classes = [AllowAny]
    authentication_classes = (CsrfExemptSessionAuthentication, BasicAuthentication)

    def patch(self, request, *args, **kwargs):
        plan_id = self.kwargs.get('plan_id')
        plan_status = request.data.get("Status", 0)
        testplan = ProjectTestPlan.objects.get(plan_id)
        if testplan:
            TestPlanService.update_testplan_status(int(plan_id), int(plan_status))
            test_plan = ProjectTestPlanSimpleSerializer(testplan)
            return response.Response(data=test_plan.data)
        else:
            return response.Response(status=status.HTTP_404_NOT_FOUND)


class ProjectTestPlanListInfoListView(APIView):
    """
    /api/home/<USER>/<days>?project=0&status=0
    """
    serializer_class = None
    permission_classes = [AllowAny]

    def get(self, request, *args, **kwargs):

        product_spaces = request.META.get("HTTP_PRODUCT_SPACE")
        project_ids = request.GET.get('project', '').split(',')
        if project_ids[0] == '':
            project_ids = ProjectService.get_project_ids_by_space(product_spaces)

        select_status_list = request.GET.get('state')
        if select_status_list == None:
            select_status_list = [1, 2, 3, 4]

        filter_days = self.kwargs.get("days", 7)
        starttime, endtime = DateTimeService.get_starttime_endtime(filter_days)

        result = list()
        planInfo = ProjectTestPlan.objects.all().filter(CreationTime__range=(starttime, endtime)).filter(
            Project__in=project_ids).filter(Status__in=select_status_list)

        project_info = Project.objects.all().filter(id__in=project_ids)
        status_dt = {1: '新建', 2: '测试中', 3: '测试完成', 4: '已归档'}

        for _project_info in project_info:
            plan_list, data = list(), dict()
            for plan in planInfo:
                if _project_info.__dict__.get('id') == plan.__dict__.get('Project'):
                    plan_list.append(self.get_plan_info(status_dt, plan))
            if len(plan_list) > 0:
                data['PBAvatar'] = _project_info.__dict__.get('PBAvatar')
                data['PBTitle'] = _project_info.__dict__.get('PBTitle')
                data['id'] = _project_info.__dict__.get('id')
                data['plan_info'] = plan_list
            if data:
                result.append(data)
        return response.Response(result)

    def get_plan_info(self, status_dt, plan):
        plan_info = dict()
        plan_info['id'] = plan.id
        plan_info['Version'] = VersionService.get_version_name(plan.Version)
        plan_info['Owner'] = UserService.get_name_by_id(plan.Owner)
        plan_info['CreationTime'] = plan.CreationTime.strftime("%Y-%m-%d %H:%M:%S")
        plan_info['requireNum'] = plan.__dict__.get('requireNum')
        plan_info['CaseCount'] = plan.CaseCount
        plan_info['Status'] = status_dt.get(plan.__dict__.get('Status'))
        plan_info['Title'] = plan.Title
        plan_info['Fortesting'] = TestPlanService.get_testplan_fortestings_topic(plan.id)
        return plan_info


class ProjectTestPlansView(APIView):
    """
    /api/project/<project_id>/testplans/<count>?status=1,2,3
    """
    authentication_classes = (CsrfExemptSessionAuthentication, BasicAuthentication)
    permission_classes = [AllowAny]

    def get(self, request, *args, **kwargs):
        project_id = self.kwargs.get('project_id')
        count = self.kwargs.get('count')
        req_status = request.GET.get('status')
        if req_status is not None:
            status_list = req_status.split(',')
            test_plans = ProjectTestPlan.objects.filter(Project=project_id).filter(
                Status__in=status_list).order_by('-id')[0:int(count)].values('id', 'Title', 'Status')
        else:
            test_plans = ProjectTestPlan.objects.filter(Project=project_id).order_by('-id')[0:int(count)].values(
                'id', 'Title', 'Status')
        result = list()
        for testplan in test_plans:
            tmp_test_plan = {}
            tmp_test_plan['id'] = testplan['id']
            tmp_test_plan['title'] = testplan['Title']
            tmp_test_plan['status'] = testplan['Status']
            tmp_test_plan['fortestingId'] = ProjectTestPlanForTesting.objects.filter(
                TestPlan=testplan['id']).values_list('Fortesting', flat=True)
            plan_case = ProjectTestPlanCase.objects.filter(TestPlan=testplan['id']).filter(IsGroup=0)
            case_count = plan_case.count()
            tmp_test_plan['total'] = case_count
            tmp_test_plan['pass'] = plan_case.filter(TestResult=1).count()
            tmp_test_plan['fail'] = plan_case.filter(TestResult=4).count()
            try:
                tmp_test_plan['passRate'] = float('%.2f' % ((tmp_test_plan['pass'] / case_count) * 100))
            except ZeroDivisionError:
                tmp_test_plan['passRate'] = 0

            result.append(tmp_test_plan)

        return response.Response(result)


class ProjectTestPlanOwnerListView(APIView):
    """
       /api/project/<project_id>/testplan_list_info?status
    """
    serializer_class = None
    permission_classes = [AllowAny]

    def get(self, request, *args, **kwargs):
        owner_id = request.user.id
        status = request.GET.get('status')
        status_list = status.split(',')
        owner_testplans = ProjectTestPlan.objects.get_owner_plans(owner_id)
        todo_testplans = owner_testplans.filter(Status__in=status_list).values('id', 'Title', 'Status', 'Project',
                                                                               'CreationTime')
        result = list()
        status_dt = {1: '新建', 2: '测试中', 3: '测试完成', 4: '已归档'}
        for testplan in todo_testplans:
            tmp_test_plan = {}
            tmp_test_plan['id'] = testplan['id']
            tmp_test_plan['title'] = testplan['Title']
            tmp_test_plan['status'] = status_dt[testplan['Status']]
            tmp_test_plan['Project'] = testplan['Project']
            tmp_test_plan['CreationTime'] = testplan['CreationTime'].strftime("%Y-%m-%d %H:%M:%S")
            result.append(tmp_test_plan)
        return response.Response(result)
