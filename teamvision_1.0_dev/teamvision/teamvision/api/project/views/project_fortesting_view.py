# coding=utf-8
"""
Created on 2014-1-5
@author: <PERSON><PERSON><PERSON><PERSON>
"""
import django_filters.rest_framework
from django.http import HttpResponse

from business.auth_user.user_service import UserService
from business.project.project_service import ProjectService
from gatesidelib.common.simplelogger import SimpleLogger
from rest_framework import generics, status, response

from teamvision.api.project.filters.project_filter import ProjectFortestingFilterSet
from teamvision.api.project.serializer import project_serializer
from rest_framework.permissions import AllowAny
from teamvision.project import models
from teamvision.home.models import FileInfo
from teamvision.settings import WEB_HOST
from teamvision.api.project.serializer import project_requirement_serializer
from rest_framework.authentication import SessionAuthentication, BasicAuthentication
from teamvision.api.project.views.CsrfExemptSessionAuthentication import CsrfExemptSessionAuthentication
from teamvision.api.project.filters import project_filter
from teamvision.api.project.filters.project_pagination import ProjectPagination
from business.project.fortesting_service import ForTestingService
from business.common.file_info_service import FileInfoService
from teamvision.project.mongo_models import FortestingMongoFile
from rest_framework.views import APIView
from django.contrib.auth.models import User


class ProjectFortestingListView(generics.ListCreateAPIView):
    """
    get:
        /api/project/<project_id>/version/<version_id>/fortestings
        get fortesting list with project_id
        FilterSet: Null
        FilterOperation:=,__in,__gt,__contains,__icontains,Range__in,__lt,!=,__isnull
    """
    serializer_class = project_serializer.ProjectForTestingSerializer
    permission_classes = [AllowAny]
    authentication_classes = (CsrfExemptSessionAuthentication, BasicAuthentication)
    pagination_class = ProjectPagination
    filterset_class = ProjectFortestingFilterSet

    def get_queryset(self):
        project_id = int(self.kwargs['project_id'])
        version_id = int(self.kwargs['version_id'])
        if str(project_id) != '0':
            if str(version_id) != '0':
                qs = models.TestApplication.objects.project_fortestings(project_id).filter(
                    VersionID=int(version_id)).order_by('-id')
            else:
                qs = models.TestApplication.objects.project_fortestings(project_id).order_by('-id')
        else:
            qs = ForTestingService.get_my_fortestings(self.request)
        testers = self.request.GET.get("Testers__in", None)
        if testers is not None:
            try:
                tester_ids = eval(testers) if isinstance(testers, str) else testers
                if isinstance(tester_ids, list):
                    # 对于JSONField，我们需要检查每个测试人员ID是否在Testers列表中
                    testers_fortesting_ids = []
                    all_fortestings = models.TestApplication.objects.all()
                    for fortesting in all_fortestings:
                        if fortesting.Testers:
                            # 检查是否有交集
                            if isinstance(fortesting.Testers, list):
                                if any(tester_id in fortesting.Testers for tester_id in tester_ids):
                                    testers_fortesting_ids.append(fortesting.id)
                            # 兼容旧格式
                            elif isinstance(fortesting.Testers, int):
                                if fortesting.Testers in tester_ids:
                                    testers_fortesting_ids.append(fortesting.id)
                            elif isinstance(fortesting.Testers, str):
                                try:
                                    old_tester_ids = eval(fortesting.Testers) if fortesting.Testers else []
                                    if isinstance(old_tester_ids, list):
                                        if any(tester_id in old_tester_ids for tester_id in tester_ids):
                                            testers_fortesting_ids.append(fortesting.id)
                                    elif isinstance(old_tester_ids, int) and old_tester_ids in tester_ids:
                                        testers_fortesting_ids.append(fortesting.id)
                                except:
                                    pass
                    qs = qs.filter(id__in=testers_fortesting_ids)
            except Exception as e:
                # 如果解析失败，返回空结果
                qs = qs.filter(id__in=[])
        return qs

    def create(self, request, *args, **kwargs):
        """
        post:
        create new fortesting
        """
        fortesting = ForTestingService.create_fortesting(request.data, request.user)
        serializer = project_serializer.ProjectForTestingSerializer(instance=fortesting)
        serializer2 = self.get_serializer(data=request.data)
        serializer2.is_valid(raise_exception=True)
        headers = self.get_success_headers(serializer.data)
        return response.Response(serializer.data, status=status.HTTP_201_CREATED, headers=headers)


class ProjectFortestingGalaxy(APIView):
    """
    /api/project/<project_id>/fortesting/galaxy
    """
    permission_classes = [AllowAny]
    authentication_classes = (CsrfExemptSessionAuthentication, BasicAuthentication)

    def create(self, request, form_data):
        for_testing = models.TestApplication()
        try:
            obj_commitor = User.objects.get(username=form_data.get('Commitor'))
            for_testing.Commitor = obj_commitor.id
            for_testing.Creator = obj_commitor.id
        except Exception as e:
            for_testing.Commitor = 10

        # 处理测试人员，支持多个测试人员
        testers_data = form_data.get('Testers')
        if testers_data:
            if isinstance(testers_data, list):
                # 如果是列表，处理多个测试人员
                tester_ids = []
                for tester_info in testers_data:
                    try:
                        if isinstance(tester_info, dict) and 'username' in tester_info:
                            obj_tester = User.objects.get(username=tester_info['username'])
                            tester_ids.append(obj_tester.id)
                        elif isinstance(tester_info, str):
                            obj_tester = User.objects.get(username=tester_info)
                            tester_ids.append(obj_tester.id)
                        elif isinstance(tester_info, int):
                            tester_ids.append(tester_info)
                    except Exception as e:
                        continue
                for_testing.Testers = tester_ids if tester_ids else []
            elif isinstance(testers_data, str):
                # 如果是字符串，处理单个测试人员
                try:
                    obj_tester = User.objects.get(username=testers_data)
                    for_testing.Testers = [obj_tester.id]
                except Exception as e:
                    for_testing.Testers = []
            elif isinstance(testers_data, int):
                # 如果是整数ID
                for_testing.Testers = [testers_data] if testers_data != 0 else []
            else:
                for_testing.Testers = []
        else:
            for_testing.Testers = []

        for_testing.ProjectID = form_data.get('ProjectID')
        for_testing.Status = 2
        for_testing.Topic = form_data.get('Topic')
        for_testing.TestingAdvice = form_data.get('TestingAdvice')
        for_testing.TestingFeature = form_data.get('TestingFeature')
        for_testing.Description = form_data.get('Description')
        for_testing.Link = form_data.get('Link')
        for_testing.RiskLevel = form_data.get('risk')
        for_testing.priority = form_data.get('priority')
        for_testing.save()

        requirement_data = form_data.get('RequirementData')
        for obj_data in requirement_data:
            requirement_obj = models.Requirement()
            requirement_obj.Title = obj_data['name']
            requirement_obj.Description = obj_data['summary']
            requirement_obj.ProjectID = form_data.get('ProjectID')
            requirement_obj.Creator = for_testing.Commitor
            requirement_obj.Status = 4
            requirement_obj.Priority = form_data.get('priority')
            requirement_obj.save()

            require_task_map = models.RequirementTaskMap()
            require_task_map.RequirementID = requirement_obj.id
            require_task_map.TaskID = for_testing.id
            require_task_map.TaskType = 2
            require_task_map.save()
        return for_testing

    def update(self, form_data):
        fortesting_id = form_data.get('id')
        for_testing = models.TestApplication.objects.get(int(fortesting_id))
        try:
            obj_commitor = User.objects.get(username=form_data.get('Commitor'))
            for_testing.Commitor = obj_commitor.id
            for_testing.Creator = obj_commitor.id
        except Exception as e:
            for_testing.Commitor = 10

        # 处理测试人员，支持多个测试人员
        testers_data = form_data.get('Testers')
        if testers_data:
            if isinstance(testers_data, list):
                # 如果是列表，处理多个测试人员
                tester_ids = []
                for tester_info in testers_data:
                    try:
                        if isinstance(tester_info, dict) and 'username' in tester_info:
                            obj_tester = User.objects.get(username=tester_info['username'])
                            tester_ids.append(obj_tester.id)
                        elif isinstance(tester_info, str):
                            obj_tester = User.objects.get(username=tester_info)
                            tester_ids.append(obj_tester.id)
                        elif isinstance(tester_info, int):
                            tester_ids.append(tester_info)
                    except Exception as e:
                        continue
                for_testing.Testers = tester_ids if tester_ids else []
            elif isinstance(testers_data, str):
                # 如果是字符串，处理单个测试人员
                try:
                    obj_tester = User.objects.get(username=testers_data)
                    for_testing.Testers = [obj_tester.id]
                except Exception as e:
                    for_testing.Testers = []
            elif isinstance(testers_data, int):
                # 如果是整数ID
                for_testing.Testers = [testers_data] if testers_data != 0 else []
            else:
                for_testing.Testers = []
        else:
            for_testing.Testers = []

        for_testing.ProjectID = form_data.get('ProjectID')
        for_testing.Status = 2
        for_testing.Topic = form_data.get('Topic')
        for_testing.TestingFeature = form_data.get('TestingFeature')
        for_testing.TestingAdvice = form_data.get('TestingAdvice')
        for_testing.Description = form_data.get('Description')
        for_testing.RiskLevel = form_data.get('RiskLevel')
        for_testing.priority = form_data.get('priority')
        for_testing.save()

        req_task_maps = models.RequirementTaskMap.objects.filter(TaskID=fortesting_id)
        for tmp in req_task_maps:
            models.Requirement.objects.filter(id=tmp.RequirementID).delete()
        req_task_maps.delete()
        requirement_data = form_data.get('RequirementData')
        for obj_data in requirement_data:
            requirement_obj = models.Requirement()
            requirement_obj.Title = obj_data['name']
            requirement_obj.Description = obj_data['summary']
            requirement_obj.ProjectID = form_data.get('ProjectID')
            requirement_obj.Creator = for_testing.Commitor
            requirement_obj.Status = 4
            requirement_obj.Priority = form_data.get('priority')
            requirement_obj.save()

            require_task_map = models.RequirementTaskMap()
            require_task_map.RequirementID = requirement_obj.id
            require_task_map.TaskID = for_testing.id
            require_task_map.TaskType = 2
            require_task_map.save()
        return for_testing

    def post(self, request):
        form_data = request.data
        fortesting_id = form_data.get('id')

        if fortesting_id:
            for_testing = self.update(form_data)
            serializer = project_serializer.ProjectForTestingSerializer(for_testing)
            return response.Response(serializer.data, status=status.HTTP_200_OK)
        else:
            for_testing = self.create(request, form_data)
            serializer = project_serializer.ProjectForTestingSerializer(for_testing)
            return response.Response(serializer.data, status=status.HTTP_201_CREATED)


class ProjectFortestingView(generics.RetrieveUpdateDestroyAPIView):
    """
    /api/project/fortesting/fortesting_id
    get,update, delete fortesting with fortesting_id
    """
    serializer_class = project_serializer.ProjectForTestingSerializer
    permission_classes = [AllowAny]
    authentication_classes = (CsrfExemptSessionAuthentication, BasicAuthentication)

    def get_object(self):
        fortesting_id = int(self.kwargs['fortesting_id'])
        fortesting = models.TestApplication.objects.get(fortesting_id)
        return fortesting

    def update(self, request, *args, **kwargs):
        partial = kwargs.pop('partial', False)
        validate_data = request.data
        fortesting_id = int(self.kwargs['fortesting_id'])
        ForTestingService.edit_fortesting(validate_data, request.user, fortesting_id)
        fortesting = self.get_object()
        serializer = self.get_serializer(fortesting, data=validate_data, partial=partial)
        serializer.is_valid(raise_exception=True)
        self.perform_update(serializer)
        if getattr(fortesting, '_prefetched_objects_cache', None):
            fortesting._prefetched_objects_cache = {}
        return response.Response(serializer.data)

    def patch(self, request, *args, **kwargs):
        partial = kwargs.pop('partial', True)
        request.data.pop('ProductSpace')
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data, partial=partial)
        serializer.is_valid(raise_exception=True)
        serializer.save()

        if getattr(instance, '_prefetched_objects_cache', None):
            # If 'prefetch_related' has been applied to a queryset, we need to
            # forcibly invalidate the prefetch cache on the instance.
            instance._prefetched_objects_cache = {}

        return response.Response(serializer.data)


class ProjectFortestingUpdateView(generics.UpdateAPIView):
    """
    /api/project/fortesting/fortesting_id/udate_property
    get,update,delete fortesting with fortesting_id
    """
    serializer_class = project_serializer.ProjectForTestingSerializer
    permission_classes = [AllowAny]
    authentication_classes = (CsrfExemptSessionAuthentication, BasicAuthentication)

    def get_object(self):
        fortesting_id = int(self.kwargs['fortesting_id'])
        fortesting = models.TestApplication.objects.get(fortesting_id)
        return fortesting


class ProjectFortestingTaskListView(generics.ListCreateAPIView):
    """
    /api/project/fortesting/<fortesting_id>/project_tasks
    req_id: requirement id
    """
    serializer_class = project_serializer.ProjectTaskSerializer
    authentication_classes = (CsrfExemptSessionAuthentication, BasicAuthentication)
    permission_classes = [AllowAny]

    def get_queryset(self):
        result = list()
        fortesting_id = self.kwargs.get("fortesting_id", None)
        if fortesting_id is not None:
            fortesting_tasks = models.FortestingTaskMap.objects.get_tasks(fortesting_id)
            task_ids = [item.TaskID for item in fortesting_tasks]
            result = models.Task.objects.all().filter(id__in=task_ids)
        return result

    def create(self, request, *args, **kwargs):
        fortesting_id = int(self.kwargs.get('fortesting_id'))
        task_ids = request.data.get("task_ids")
        req_tasks = ForTestingService.create_fortesting_task(task_ids, fortesting_id)
        return response.Response({"LinkCount": len(req_tasks)}, status=status.HTTP_201_CREATED)


class ProjectFortestingTaskView(generics.RetrieveUpdateDestroyAPIView):
    """
    /api/project/reqirement/<fortesting_id>/task/<task_id>
    fortesting_id: fortesting id
    task_id: linked task_id
    """
    serializer_class = project_requirement_serializer.RequirementTaskSerializer
    authentication_classes = (CsrfExemptSessionAuthentication, BasicAuthentication)
    permission_classes = [AllowAny]

    def get_object(self):
        result = None
        fortesting_id = self.kwargs.get("fortesting_id", None)
        task_id = self.kwargs.get("task_id", None)
        if fortesting_id is not None and task_id is not None:
            req_tasks = models.FortestingTaskMap.objects.get_tasks(fortesting_id).filter(TaskID=int(task_id))
            if len(req_tasks) > 0:
                result = req_tasks[0]
        return result

    def destroy(self, request, *args, **kwargs):
        fortesting_id = self.kwargs.get("fortesting_id", None)
        task_id = self.kwargs.get("task_id", None)
        if fortesting_id is not None and task_id is not None:
            req_tasks = models.FortestingTaskMap.objects.get_tasks(fortesting_id).filter(TaskID=int(task_id))
            req_tasks.delete()
        return response.Response(status=status.HTTP_204_NO_CONTENT)


class ProjectFortestingUpdateStatusView(generics.UpdateAPIView):
    """
    /api/project/fortesting/fortesting_id/update_status
    update fortesting status
    """
    serializer_class = project_serializer.ProjectForTestingSerializer
    permission_classes = [AllowAny]
    authentication_classes = (CsrfExemptSessionAuthentication, BasicAuthentication)

    def patch(self, request, *args, **kwargs):
        fortesting_id = int(self.kwargs['fortesting_id'])
        status = request.data.get("Status", 0)
        result = [False, ""]
        try:
            result = ForTestingService.update_fortesting_status(request.user, fortesting_id, status)
        except Exception as ex:
            SimpleLogger.exception(ex)
        return response.Response({'Status': result[0], 'message': result[1]})


class ProjectFortestingAttachementView(generics.CreateAPIView, generics.DestroyAPIView, generics.RetrieveAPIView):
    """
    /api/project/fortesting/upload_files
    upload fortesing attachment
    """
    serializer_class = project_serializer.ProjectForTestingSerializer
    permission_classes = [AllowAny]
    authentication_classes = (CsrfExemptSessionAuthentication, BasicAuthentication)
    pagination_class = ProjectPagination

    def post(self, request, *args, **kwargs):
        message = ForTestingService.attachments_upload_handler(request.FILES['file'])
        if message[0] != '0':
            return response.Response(
                {'file_id': message[0], 'url': WEB_HOST + '/api/project/fortesting/download_file/' + str(message[0])})
        else:
            return response.Response(status=status.HTTP_417_EXPECTATION_FAILED)

    def delete(self, request, *args, **kwargs):
        file_id = kwargs.get('file_id')
        ForTestingService.delete_file(file_id)
        return response.Response(status=status.HTTP_204_NO_CONTENT)

    def get(self, request, *args, **kwargs):
        result = True
        try:
            file_id = kwargs.get('file_id')
            file_info = FileInfo.objects.get(int(file_id))
            result = FileInfoService.get_file(int(file_id), FortestingMongoFile)
        except Exception as ex:
            result = str(ex)
            SimpleLogger.exception(ex)
        return HttpResponse(result, content_type="application/" + file_info.FileSuffixes)
