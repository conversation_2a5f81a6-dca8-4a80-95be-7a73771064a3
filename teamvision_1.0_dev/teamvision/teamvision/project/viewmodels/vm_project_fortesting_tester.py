# coding=utf-8
'''
Created on 2015-11-17

@author: <PERSON><PERSON><PERSON><PERSON>
'''
from teamvision.project.models import ProjectRole, ProjectMember
from django.contrib.auth.models import User
from business.ucenter.account_service import AccountService
from teamvision.project.viewmodels.vm_project_member import VM_ProjectMember


class VM_FortestingTester(object):
    '''
    classdocs
    '''

    def __init__(self, project_id, user, role_menu, login_user, fortesting):
        '''
        Constructor
        '''
        self.member = user
        self.project_id = project_id
        self.role_menu = role_menu
        self.fortesting = fortesting

    def is_fortesting_tester(self):
        """检查用户是否是测试人员，支持JSONField格式"""
        result = ""
        if self.fortesting is not None and self.fortesting.Testers:
            try:
                # 如果是列表格式（JSONField）
                if isinstance(self.fortesting.Testers, list):
                    if self.member.id in self.fortesting.Testers:
                        result = "fa-check"
                # 兼容旧的字符串格式
                elif isinstance(self.fortesting.Testers, str):
                    try:
                        tester_ids = eval(self.fortesting.Testers) if self.fortesting.Testers else []
                        if isinstance(tester_ids, list) and self.member.id in tester_ids:
                            result = "fa-check"
                        elif isinstance(tester_ids, int) and self.member.id == tester_ids:
                            result = "fa-check"
                    except:
                        # 如果eval失败，尝试作为单个ID处理
                        if self.fortesting.Testers.isdigit() and self.member.id == int(self.fortesting.Testers):
                            result = "fa-check"
                # 兼容整数格式
                elif isinstance(self.fortesting.Testers, int):
                    if self.member.id == self.fortesting.Testers:
                        result = "fa-check"
            except Exception:
                # 如果出现任何异常，返回空字符串
                pass
        return result

    def member_name(self):
        result = self.member.username
        if self.member.last_name and self.member.first_name:
            result = self.member.last_name + self.member.first_name
        return result

    def member_avatar(self):
        result = "/static/global/images/fruit-avatar/Fruit-1.png"
        if self.member.extend_info:
            result = AccountService.get_avatar_url(self.member)
        return result
